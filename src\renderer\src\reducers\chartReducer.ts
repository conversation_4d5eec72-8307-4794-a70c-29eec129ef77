import {
  SpectralSeries,
  convertSeries,
  findMaxInRange,
  findMinInRange,
  calculateAverageInRange,
  calculateFWHM,

} from '../lib/spectralUtils'

// 坐标轴设置接口
export interface AxisSettings {
  start: number | null
  end: number | null
  step: number | null
  min: number | null
  max: number | null
}

// 定义字体大小设置接口
export interface FontSizeSettings {
  axisLabel: number
  axisName: number
  legendText: number
}

// 定义状态类型
export interface ChartState {
  rawData: any[]
  activeMode: 'T' | 'R' | 'Abs'
  chartData: SpectralSeries[]
  chartTitle: string
  feature1Data: Feature1DataRow[]
  feature2Data: Feature2DataRow[]
  activeFeature2RowId: number | null
  activeSeriesId: string | null
  activeTab: 'feature1' | 'feature2'
  xAxis?: AxisSettings
  yAxis?: AxisSettings
  fontSizes?: FontSizeSettings
}

// 功能一的数据行接口
export interface Feature1DataRow {
  id: number
  startWavelength: string
  endWavelength: string
  maxValue: string
  minValue: string
  avgValue: string
  selected: boolean
}

// 功能二的数据行接口
export interface Feature2DataRow {
  id: number
  startWavelength: string
  endWavelength: string
  centerWavelength: string
  halfWidth: string
  maxTransmittance: string
  wavelength1: string
  wavelength2: string
  wavelength20_1: string
  wavelength20_2: string
  wavelength10_1: string
  wavelength10_2: string
  selected: boolean
}

// 定义 Action 类型
export type ChartAction =
  | { type: 'SET_CHART_DATA'; payload: SpectralSeries[] }
  | { type: 'SET_RAW_DATA'; payload: any[] }
  | { type: 'SET_ACTIVE_MODE'; payload: 'T' | 'R' | 'Abs' }
  | { type: 'SET_CHART_TITLE'; payload: string }
  | { type: 'SET_CHART_COLOR'; payload: { seriesId: string; color: string } }
  | { type: 'SET_SERIES_NAME'; payload: { seriesId: string; name: string } }
  | { type: 'SET_SERIES_LINE_WIDTH'; payload: { seriesId: string; lineWidth: number } }
  | { type: 'SET_ACTIVE_SERIES'; payload: string }
  | { type: 'SET_FEATURE1_DATA'; payload: Feature1DataRow[] }
  | { type: 'SET_FEATURE2_DATA'; payload: Feature2DataRow[] }
  | {
      type: 'UPDATE_FEATURE1_ROW'
      payload: { id: number; field: keyof Feature1DataRow; value: string }
    }
  | {
      type: 'UPDATE_FEATURE2_ROW'
      payload: { id: number; field: keyof Feature2DataRow; value: string }
    }
  | { type: 'TOGGLE_FEATURE1_SELECTION'; payload: number }
  | { type: 'TOGGLE_FEATURE2_SELECTION'; payload: number }
  | { type: 'SET_ACTIVE_FEATURE2_ROW'; payload: number }
  | { type: 'SET_ACTIVE_TAB'; payload: 'feature1' | 'feature2' }
  | { type: 'ADD_FEATURE1_ROW' }
  | { type: 'ADD_FEATURE2_ROW' }
  | { type: 'CALCULATE_FEATURE1_VALUES'; payload: number }
  | { type: 'CALCULATE_ALL_FEATURE1_VALUES' }
  | { type: 'CALCULATE_FEATURE2_VALUES'; payload: number }
  | { type: 'CALCULATE_ALL_FEATURE2_VALUES' }
  | { type: 'SET_X_AXIS'; payload: Omit<AxisSettings, 'min' | 'max'> }
  | { type: 'SET_Y_AXIS'; payload: Omit<AxisSettings, 'min' | 'max'> }
  | { type: 'SET_FONT_SIZES'; payload: FontSizeSettings }
  | { type: 'RESET_Y_AXIS' }
  | { type: 'RESET_X_AXIS' }
  | { type: 'CLEAR_ANNOTATIONS' }

// 初始状态
export const initialChartState: ChartState = {
  activeMode: 'T',
  rawData: [],
  chartData: [
    {
      id: '1',
      name: '123',
      mode: 'T',
      color: 'red',
      visible: true,
      lineWidth: 2,
      data: [
        { wavelength: 100, value: 10 },
        { wavelength: 110, value: 20 },
        { wavelength: 120, value: 40 },
        { wavelength: 130, value: 60 },
        { wavelength: 140, value: 80 },
        { wavelength: 150, value: 100 }, // 峰值
        { wavelength: 160, value: 80 },
        { wavelength: 170, value: 60 },
        { wavelength: 180, value: 40 },
        { wavelength: 190, value: 20 },
        { wavelength: 200, value: 10 }
      ]
    }
  ],
  chartTitle: '数据分析图表',
  activeSeriesId: '1', // 默认使用第一条曲线
  feature1Data: [
    {
      id: 1,
      startWavelength: '',
      endWavelength: '',
      maxValue: '',
      minValue: '',
      avgValue: '',
      selected: true
    }
  ],
  feature2Data: [
    {
      id: 1,
      startWavelength: '',
      endWavelength: '',
      centerWavelength: '',
      halfWidth: '',
      maxTransmittance: '',
      wavelength1: '',
      wavelength2: '',
      wavelength20_1: '',
      wavelength20_2: '',
      wavelength10_1: '',
      wavelength10_2: '',
      selected: true
    }
  ],
  activeFeature2RowId: 1, // 默认选中第一行
  activeTab: 'feature1', // 默认选中功能一标签页
  // 默认坐标轴设置
  xAxis: {
    start: null,
    end: null,
    step: null,
    min: 0,
    max: 6
  },
  yAxis: {
    min: 0,
    max: 6,
    start: null,
    end: null,
    step: null
  },
  // 默认字体大小设置
  fontSizes: {
    axisLabel: 12,
    axisName: 12,
    legendText: 12
  }
}

// Reducer 函数
export function chartReducer(state: ChartState, action: ChartAction): ChartState {
  switch (action.type) {
    case 'SET_CHART_DATA':
      // 当设置图表数据时，如果有数据，自动更新 activeMode
      // console.log(action.payload)
      if (action.payload.length > 0 && action.payload[0].mode) {
        const firstSeriesMode = action.payload[0].mode as 'T' | 'R' | 'Abs'
       
        return {
          ...state,
          chartData: action.payload,
          activeMode: firstSeriesMode,
          activeSeriesId: action.payload[0].id // 默认选中第一条曲线
        }
      }
      return {
        ...state,
        chartData: action.payload
      }
    case 'SET_RAW_DATA':
      return {
        ...state,
        rawData: action.payload
      }

    case 'RESET_Y_AXIS':
      return {
        ...state,
        yAxis: {
          min: null,
          max: null,
          start: null,
          end: null,
          step: null
        }
      }
    case 'RESET_X_AXIS':
      return {
        ...state,
        xAxis: {
          min: null,
          max: null,
          start: null,
          end: null,
          step: null
        }
      }
    case 'SET_ACTIVE_MODE': {
      // 当更改模式时，转换所有曲线数据
      const prevMode = state.activeMode
      const newMode = action.payload

      if (prevMode === newMode) {
        return state
      }

      const convertedData = state.chartData.map((series) => ({
        ...series,
        mode: newMode,
        data: convertSeries(series.data, prevMode, newMode)
      }))
  

      return {
        ...state,
        activeMode: newMode,
        chartData: convertedData
        // xAxis: {

        //   start:minXValue,
        //   end:maxXValue,
        //   step:1,
        //   min:minXValue,
        //   max:maxXValue
        // },

        // yAxis: {
        //   start:minYValue,
        //   end:maxYValue,
        //   step:1,
        //   min:minYValue,
        //   max:maxYValue
        // }
      }
    }

    case 'SET_CHART_TITLE':
      return {
        ...state,
        chartTitle: action.payload
      }

    case 'SET_CHART_COLOR':
      return {
        ...state,
        chartData: state.chartData.map((series) =>
          series.id === action.payload.seriesId
            ? { ...series, color: action.payload.color }
            : series
        )
      }

    case 'SET_SERIES_NAME':
      return {
        ...state,
        chartData: state.chartData.map((series) =>
          series.id === action.payload.seriesId
            ? { ...series, name: action.payload.name }
            : series
        )
      }

    case 'SET_SERIES_LINE_WIDTH':
      return {
        ...state,
        chartData: state.chartData.map((series) =>
          series.id === action.payload.seriesId
            ? { ...series, lineWidth: action.payload.lineWidth }
            : series
        )
      }

    case 'SET_FEATURE1_DATA':
      return {
        ...state,
        feature1Data: action.payload
      }

    case 'SET_FEATURE2_DATA':
      return {
        ...state,
        feature2Data: action.payload
      }

    case 'UPDATE_FEATURE1_ROW':
      return {
        ...state,
        feature1Data: state.feature1Data.map((row) =>
          row.id === action.payload.id
            ? { ...row, [action.payload.field]: action.payload.value }
            : row
        )
      }

    case 'UPDATE_FEATURE2_ROW':
      return {
        ...state,
        feature2Data: state.feature2Data.map((row) =>
          row.id === action.payload.id
            ? { ...row, [action.payload.field]: action.payload.value }
            : row
        )
      }

    case 'TOGGLE_FEATURE1_SELECTION':
      return {
        ...state,
        feature1Data: state.feature1Data.map((row) =>
          row.id === action.payload ? { ...row, selected: !row.selected } : row
        )
      }

    case 'TOGGLE_FEATURE2_SELECTION':
      return {
        ...state,
        feature2Data: state.feature2Data.map((row) =>
          row.id === action.payload ? { ...row, selected: !row.selected } : row
        )
      }

    case 'SET_ACTIVE_FEATURE2_ROW':
      return {
        ...state,
        activeFeature2RowId: action.payload
      }

    case 'SET_ACTIVE_TAB':
      return {
        ...state,
        activeTab: action.payload
      }

    case 'ADD_FEATURE1_ROW':
      const newFeature1Id = Math.max(...state.feature1Data.map((row) => row.id)) + 1
      return {
        ...state,
        feature1Data: [
          ...state.feature1Data,
          {
            id: newFeature1Id,
            startWavelength: '',
            endWavelength: '',
            maxValue: '',
            minValue: '',
            avgValue: '',
            selected: true
          }
        ]
      }

    case 'ADD_FEATURE2_ROW':
      const newFeature2Id = Math.max(...state.feature2Data.map((row) => row.id)) + 1
      return {
        ...state,
        feature2Data: [
          ...state.feature2Data,
          {
            id: newFeature2Id,
            startWavelength: '',
            endWavelength: '',
            centerWavelength: '',
            halfWidth: '',
            maxTransmittance: '',
            wavelength1: '',
            wavelength2: '',
            wavelength20_1: '',
            wavelength20_2: '',
            wavelength10_1: '',
            wavelength10_2: '',
            selected: true
          }
        ]
      }
    
    case 'CALCULATE_ALL_FEATURE1_VALUES':
      {
        if (!state.chartData.length) {
          return state
        }

        // 使用当前选中的曲线，如果没有选中或找不到，则使用第一条曲线
        const activeSeries = state.activeSeriesId 
          ? state.chartData.find(series => series.id === state.activeSeriesId) 
          : state.chartData[0];
        
        if (!activeSeries) {
          return state;
        }

        return {
          ...state,
          feature1Data: state.feature1Data.map((row) => {
            if (!row.startWavelength || !row.endWavelength) {
              return row
            }

            const startWavelength = parseFloat(row.startWavelength)
            const endWavelength = parseFloat(row.endWavelength)

            if (isNaN(startWavelength) || isNaN(endWavelength)) {
              return row
            }

            const maxResult = findMaxInRange(activeSeries.data, startWavelength, endWavelength)
            const minResult = findMinInRange(activeSeries.data, startWavelength, endWavelength)
            const avgResult = calculateAverageInRange(activeSeries.data, startWavelength, endWavelength)

            return {
              ...row,
              maxValue: maxResult ? maxResult.value.toFixed(4) : '',
              minValue: minResult ? minResult.value.toFixed(4) : '',
              avgValue: avgResult ? avgResult.toFixed(4) : ''
            }
          })
        }
      }

    case 'CALCULATE_FEATURE2_VALUES': 
      {
        const rowId2 = action.payload
        const rowToUpdate2 = state.feature2Data.find((row) => row.id === rowId2)

        if (
          !rowToUpdate2 ||
          !state.chartData.length ||
          !rowToUpdate2.startWavelength ||
          !rowToUpdate2.endWavelength
        ) {
          return state
        }

        const startWavelength2 = parseFloat(rowToUpdate2.startWavelength)
        const endWavelength2 = parseFloat(rowToUpdate2.endWavelength)

        if (isNaN(startWavelength2) || isNaN(endWavelength2)) {
          return state
        }

        // 使用当前选中的曲线，如果没有选中或找不到，则使用第一条曲线
        const series2 = state.activeSeriesId 
          ? state.chartData.find(series => series.id === state.activeSeriesId) 
          : state.chartData[0];
        
        if (!series2) {
          return state;
        }
        
        console.log(
          `计算Feature2: 波长范围 ${startWavelength2} - ${endWavelength2}, 数据点数: ${series2.data.length}`
        )

        // 找到最大透过率及其波长
        const maxResult2 = findMaxInRange(series2.data, startWavelength2, endWavelength2)
        if (!maxResult2) {
          console.log('未找到最大值，可能是波长范围内没有数据点')
          return state
        }

        // 计算半高宽和相关波长
        const peakWavelength = maxResult2.wavelength
        const peakValue = maxResult2.value
        console.log(`找到峰值: 波长=${peakWavelength}, 值=${peakValue}`)

        // 计算FWHM和半高点波长，传递波长范围
        const fwhmResult = calculateFWHM(
          series2.data,
          peakWavelength,
          peakValue,
          startWavelength2,
          endWavelength2
        )

        // 中心波长 = (λ1 + λ2) / 2
        const centerWavelength =
          fwhmResult.leftWavelength !== null && fwhmResult.rightWavelength !== null
            ? ((fwhmResult.leftWavelength + fwhmResult.rightWavelength) / 2).toFixed(4)
            : ''

        return {
          ...state,
          feature2Data: state.feature2Data.map((row) =>
            row.id === rowId2
              ? {
                  ...row,
                  maxTransmittance: maxResult2.value.toFixed(4),
                  centerWavelength: centerWavelength,
                  halfWidth: fwhmResult.fwhm !== null ? fwhmResult.fwhm.toFixed(4) : '',
                  wavelength1:
                    fwhmResult.leftWavelength !== null ? fwhmResult.leftWavelength.toFixed(4) : '',
                  wavelength2:
                    fwhmResult.rightWavelength !== null ? fwhmResult.rightWavelength.toFixed(4) : '',
                  wavelength20_1:
                    fwhmResult.leftWavelength20 !== null ? fwhmResult.leftWavelength20.toFixed(4) : '',
                  wavelength20_2:
                    fwhmResult.rightWavelength20 !== null ? fwhmResult.rightWavelength20.toFixed(4) : '',
                  wavelength10_1:
                    fwhmResult.leftWavelength10 !== null ? fwhmResult.leftWavelength10.toFixed(4) : '',
                  wavelength10_2:
                    fwhmResult.rightWavelength10 !== null ? fwhmResult.rightWavelength10.toFixed(4) : ''
                }
              : row
          )
        }
      }

    case 'CALCULATE_ALL_FEATURE2_VALUES': 
      {
        if (!state.chartData.length) {
          return state
        }

        // 使用当前选中的曲线，如果没有选中或找不到，则使用第一条曲线
        const seriesData2 = state.activeSeriesId 
          ? state.chartData.find(series => series.id === state.activeSeriesId) 
          : state.chartData[0];
        
        if (!seriesData2) {
          return state;
        }
        
        console.log(`计算所有Feature2行: 数据点数: ${seriesData2.data.length}`)

        return {
          ...state,
          feature2Data: state.feature2Data.map((row) => {
            if (!row.startWavelength || !row.endWavelength) {
              return row
            }

            const startWavelength = parseFloat(row.startWavelength)
            const endWavelength = parseFloat(row.endWavelength)

            if (isNaN(startWavelength) || isNaN(endWavelength)) {
              return row
            }

            console.log(`计算Feature2行: 波长范围 ${startWavelength} - ${endWavelength}`)

            // 找到最大透过率及其波长
            const maxResult = findMaxInRange(seriesData2.data, startWavelength, endWavelength)
            if (!maxResult) {
              console.log('未找到最大值，可能是波长范围内没有数据点')
              return row
            }

            // 计算半高宽和相关波长
            const peakWavelength = maxResult.wavelength
            const peakValue = maxResult.value
            console.log(`找到峰值: 波长=${peakWavelength}, 值=${peakValue}`)

            // 计算FWHM和半高点波长，传递波长范围
            const fwhmResult = calculateFWHM(
              seriesData2.data,
              peakWavelength,
              peakValue,
              startWavelength,
              endWavelength
            )

            // 中心波长 = (λ1 + λ2) / 2
            const centerWavelength =
              fwhmResult.leftWavelength !== null && fwhmResult.rightWavelength !== null
                ? ((fwhmResult.leftWavelength + fwhmResult.rightWavelength) / 2).toFixed(4)
                : ''
            console.log(fwhmResult)
            return {
              ...row,
              maxTransmittance: maxResult.value.toFixed(4),
              centerWavelength: centerWavelength,
              halfWidth: fwhmResult.fwhm !== null ? fwhmResult.fwhm.toFixed(4) : '',
              wavelength1:
                fwhmResult.leftWavelength !== null ? fwhmResult.leftWavelength.toFixed(4) : '',
              wavelength2:
                fwhmResult.rightWavelength !== null ? fwhmResult.rightWavelength.toFixed(4) : '',
              wavelength20_1:
                fwhmResult.leftWavelength20 !== null ? fwhmResult.leftWavelength20.toFixed(4) : '',
              wavelength20_2:
                fwhmResult.rightWavelength20 !== null ? fwhmResult.rightWavelength20.toFixed(4) : '',
              wavelength10_1:
                fwhmResult.leftWavelength10 !== null ? fwhmResult.leftWavelength10.toFixed(4) : '',
              wavelength10_2:
                fwhmResult.rightWavelength10 !== null ? fwhmResult.rightWavelength10.toFixed(4) : ''
            }
          })
        }
      }

    case 'SET_X_AXIS':
      return {
        ...state,
        xAxis: {
          ...(state.xAxis || { min: 0, max: 0 }),
          ...action.payload
        }
      }

    case 'SET_Y_AXIS':
      return {
        ...state,
        yAxis: {
          ...(state.yAxis || { min: 0, max: 0 }),
          ...action.payload
        }
      }

    case 'SET_FONT_SIZES':
      return {
        ...state,
        fontSizes: action.payload
      }

    case 'CLEAR_ANNOTATIONS':
      return {
        ...state,
        feature1Data: state.feature1Data.map((row) => ({ ...row, selected: false })),
        feature2Data: state.feature2Data.map((row) => ({ ...row, selected: false })),
        activeFeature2RowId: null
      }

    case 'SET_ACTIVE_SERIES':
      return {
        ...state,
        activeSeriesId: action.payload
      }

    default:
      return state
  }
}
