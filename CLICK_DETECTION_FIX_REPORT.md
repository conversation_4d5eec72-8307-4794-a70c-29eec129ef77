# 点击检测修复报告

## 问题描述
点击图表任意位置都会弹出添加标记的右键菜单，而不是只在数据点附近才弹出。

## 问题根源分析
在 `handleContextMenu` 函数中，虽然计算了最接近的数据点距离，但是**没有检查这个距离是否在合理范围内**。代码逻辑如下：

```typescript
// 问题代码
processedSeries.forEach((series) => {
  series.data.forEach((point: any) => {
    // 计算距离但没有阈值检查
    const distance = Math.sqrt(Math.pow(px - xValue, 2) + Math.pow(py - yValue, 2))
    if (distance < minDistance) {
      minDistance = distance
      closestPoint = { x: px, y: py, seriesName: series.name }
    }
  })
})

// 无条件显示右键菜单
setContextMenuVisible(true)
```

这意味着无论点击图表的任何位置，都会找到一个"最接近"的数据点并显示右键菜单。

## 解决方案
采用**像素距离检测**方案，确保只有在真正接近数据点时才显示右键菜单。

### 核心改进

#### 1. 使用像素距离而非数据坐标距离
```typescript
// 将数据点坐标转换为像素坐标
const dataPointPixel = chartRef.current!.convertToPixel({ seriesIndex: 0 }, [px, py])

// 计算像素距离
const pixelDistance = Math.sqrt(
  Math.pow(dataPointPixel[0] - x, 2) + Math.pow(dataPointPixel[1] - y, 2)
)
```

#### 2. 设置合理的像素距离阈值
```typescript
const PIXEL_THRESHOLD = 25 // 25像素范围内才认为是点击了数据点
foundNearbyDataPoint = minPixelDistance <= PIXEL_THRESHOLD
```

#### 3. 条件性显示右键菜单
```typescript
// 只有在数据点附近或者有标注点时才显示右键菜单
if (foundNearbyDataPoint || nearby.length > 0) {
  setContextMenuVisible(true)
}
```

#### 4. 优化标注点检测
```typescript
// 标注点的检测范围稍大一些，便于选中
const annotationPixel = chartRef.current!.convertToPixel({ seriesIndex: 0 }, [annotation.x, annotation.y])
const pixelDistance = Math.sqrt(
  Math.pow(annotationPixel[0] - x, 2) + Math.pow(annotationPixel[1] - y, 2)
)
return pixelDistance < 35 // 35像素范围
```

## 修改文件
- `src\renderer\src\components\SpectralChart.tsx`

## 修改内容
1. **距离计算方式**：从数据坐标距离改为像素距离
2. **阈值检测**：添加25像素的检测阈值
3. **条件判断**：只有在阈值范围内才显示右键菜单
4. **标注检测优化**：标注点使用35像素的检测范围
5. **代码清理**：修复未使用变量警告

## 技术细节

### 像素距离 vs 数据坐标距离
- **数据坐标距离**：在数据空间中计算，不考虑图表缩放和显示比例
- **像素距离**：在屏幕像素空间中计算，更符合用户的视觉感受

### 阈值设置
- **数据点检测**：25像素 - 确保精确点击
- **标注点检测**：35像素 - 便于选中已有标注

### 坐标转换
使用ECharts的 `convertToPixel` 方法将数据坐标转换为像素坐标：
```typescript
const dataPointPixel = chartRef.current!.convertToPixel({ seriesIndex: 0 }, [px, py])
```

## 预期效果
- ✅ 只有在数据点附近（25像素内）右键点击才会显示添加标注菜单
- ✅ 在标注点附近（35像素内）右键点击可以编辑/删除标注
- ✅ 在空白区域右键点击不会显示任何菜单
- ✅ 提升用户体验，避免误操作

## 建议测试步骤
1. **正常情况**：在数据点附近右键点击，应该显示"添加标注"菜单
2. **边界情况**：在数据点稍远处右键点击，不应该显示菜单
3. **空白区域**：在图表空白区域右键点击，不应该显示菜单
4. **标注编辑**：在已有标注附近右键点击，应该显示编辑/删除菜单
5. **缩放测试**：在不同缩放级别下测试检测精度

修复完成时间：2025-07-17
