import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from './ui/button'
import { FolderOpen, Settings, Eye, EyeOff } from 'lucide-react'
import { SpectralSeries, SpectralDataPoint } from '../lib/spectralUtils'
import * as XLSX from 'xlsx'
import { toast } from 'sonner'
import { useChart } from '../contexts/ChartContext'
import { useNavigate } from 'react-router-dom'
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from './ui/dialog'
import { Checkbox } from './ui/checkbox'

// 定义数据类型
type ExcelData = Array<Array<string | number>>

const FilePath: React.FC = () => {
  const { dispatch } = useChart()

  const navigate = useNavigate()
  const [filePath, setFilePath] = useState<string>('')

  const [series, setSeries] = useState<SpectralSeries[]>([])
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  // 光谱计算相关状态
  const [selectedSeriesA, setSelectedSeriesA] = useState<string>('')
  const [selectedSeriesB, setSelectedSeriesB] = useState<string>('')

  // 监听菜单事件
  useEffect(() => {
    // 设置菜单事件监听
    const removeListener = window.api.fileOperations.onMenuEvent((event, data) => {
      if (event === 'file-opened') {
        handleFileOpen(data)
      }
      if (event === 'open-activation-page') {
        // 导航到激活页面
        navigate('/activationStatus')
      }
      if (event === 'open-activation-page2') {
        // 导航到激活页面
        navigate('/activation')
      }
    })

    // 组件卸载时清理监听器
    return () => {
      // 如果 removeListener 是一个函数，则调用它来移除监听器
      if (typeof removeListener === 'function') {
        // @ts-ignore - Electron IPC返回类型不确定
        removeListener()
      }
    }
  }, [navigate])

  // 重置表格数据和图表标注
  const resetTableAndAnnotations = (): void => {
    // 重置功能一表格数据
    dispatch({
      type: 'SET_FEATURE1_DATA',
      payload: [
        {
          id: 1,
          startWavelength: '',
          endWavelength: '',
          maxValue: '',
          minValue: '',
          avgValue: '',
          selected: true
        }
      ]
    })

    // 重置功能二表格数据
    dispatch({
      type: 'SET_FEATURE2_DATA',
      payload: [
        {
          id: 1,
          startWavelength: '',
          endWavelength: '',
          centerWavelength: '',
          halfWidth: '',
          maxTransmittance: '',
          wavelength1: '',
          wavelength2: '',
          selected: true,
          wavelength20_1: '',
          wavelength20_2: '',
          wavelength10_1: '',
          wavelength10_2: ''
        }
      ]
    })

    // 重置活动行
    dispatch({ type: 'SET_ACTIVE_FEATURE2_ROW', payload: 1 })

    // 设置默认选中功能一标签页
    dispatch({ type: 'SET_ACTIVE_TAB', payload: 'feature1' })
  }

  // 处理通过菜单打开的文件
  const handleFileOpen = async (filePath: string): Promise<void> => {
    try {
      setFilePath(filePath)

      // 读取文件内容
      const fileContent = await window.api.fileOperations.readFile(filePath)

      // 解析 Excel 文件
      const workbook = XLSX.read(fileContent, { type: 'buffer' })
      const firstSheetName = workbook.SheetNames[0]
      const worksheet = workbook.Sheets[firstSheetName]

      // 转换为数组
      const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 }) as ExcelData

      // 重置表格数据和图表标注
      resetTableAndAnnotations()

      dispatch({ type: 'SET_RAW_DATA', payload: data })

      // 处理数据并生成图表数据
      processData(data)

      // 设置图表标题为文件名
      const fileName = filePath.split(/[\\/]/).pop() || '未命名文件'
      dispatch({ type: 'SET_CHART_TITLE', payload: fileName })
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      console.error('Error reading file:', error)
      toast.error(`读取文件时出错: ${errorMessage}`)
    }
  }

  // 处理选择文件
  const handleSelectFile = async (): Promise<void> => {
    try {
      // 使用 Electron 的 dialog API 选择文件
      const result = await window.api.fileOperations.openFileDialog({
        filters: [{ name: 'Excel Files', extensions: ['xlsx', 'xls', 'csv'] }]
      })

      if (result.canceled || !result.filePaths || result.filePaths.length === 0) {
        return
      }

      const selectedFilePath = result.filePaths[0]
      handleFileOpen(selectedFilePath)
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      console.error('Error selecting file:', error)
      toast.error(`选择文件时出错: ${errorMessage}`)
    }
  }

  // 处理数据并生成图表数据
  const processData = (data: ExcelData): void => {
      console.log(data)
    if (!data || data.length === 0) {
      console.error('Invalid data format')
      return
    }

    try {
      const seriesData: SpectralSeries[] = []
      const colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown', 'pink', 'gray']

      // 假设第一列是波长，从第二列开始是各条曲线的数据
      const numColumns = data[0]?.length || 0
      if (numColumns < 2) {
        const seriesPoints: SpectralDataPoint[] = []
        for(let i=0;i<data.length;i++) {
       
          const row = data[i]
     
          if (row && row.length > 1 && typeof row[0] === 'number' && typeof row[1] === 'number') {
            seriesPoints.push({
              wavelength: Number(row[0]), // 确保波长是数字
              value: Number(row[1]) // 确保值是数字
            })
          
          }
        }
        const seriesName = String(data[0][0] )
        const cellValue = data[1][1]
        const mode = typeof cellValue === 'string' && cellValue.includes('R') ? 'R' : 'T'
     
        seriesData.push({
          id: `series_0`,
          name: seriesName,
          mode: mode,
          color: 'red',
          visible: true, // 只有第一条曲线默认显示
          data: seriesPoints
        })
        
      } else {
        // 为每一列数据创建一条曲线（除了第一列波长）
        for (let colIndex = 0; colIndex < numColumns; colIndex+=2) {
          const seriesPoints: SpectralDataPoint[] = []
     
          // 跳过表头行，从第二行开始处理数据
          for (let rowIndex = 1; rowIndex < data.length; rowIndex++) {
            const row = data[rowIndex]
   
            if (
              row &&
              row.length > colIndex &&
              typeof row[0] === 'number' &&
              typeof row[colIndex] === 'number'
            ) {
              seriesPoints.push({
                wavelength: Number(row[colIndex]), // 确保波长是数字
                value: Number(row[colIndex+1]) // 确保值是数字
              })
            }
          }

          if (seriesPoints.length > 0) {
            const seriesName = String(data[0][colIndex === 1 ? 0 : colIndex] )
            console.log(seriesName,seriesPoints)
            const cellValue = data[1] && data[1][colIndex]
            const mode = typeof cellValue === 'string' && cellValue.includes('R') ? 'R' : 'T'

            seriesData.push({
              id: `series_${colIndex}`,
              name: seriesName,
              mode: mode,
              color: colors[(colIndex - 1) % colors.length] || 'red',
              visible: colIndex === 0, // 只有第一条曲线默认显示
              data: seriesPoints
            })
          }
        }
      }
 
      setSeries(seriesData)

      // 使用 dispatch 更新图表数据
      dispatch({ type: 'SET_CHART_DATA', payload: seriesData })

      // 如果有数据，设置第一条曲线为活跃曲线
      if (seriesData.length > 0) {
        console.log(seriesData[0].id )
        dispatch({ type: 'SET_ACTIVE_SERIES', payload: seriesData[0].id })
      }

      // 重置坐标轴
      dispatch({ type: 'RESET_X_AXIS' })
      dispatch({ type: 'RESET_Y_AXIS' })
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      console.error('Error processing data:', error)
      toast.error(`处理数据时出错: ${errorMessage}`)
    }
  }

  // 计算可见曲线的坐标轴范围
  const calculateAxisRanges = (
    visibleSeries: SpectralSeries[]
  ): {
    xAxis: { start: number; end: number; step: number; min: number; max: number }
    yAxis: { start: number; end: number; step: number; min: number; max: number }
  } => {
    if (visibleSeries.length === 0) {
      return {
        xAxis: { start: 0, end: 100, step: 10, min: 0, max: 100 },
        yAxis: { start: 0, end: 100, step: 10, min: 0, max: 100 }
      }
    }

    let minX = Infinity,
      maxX = -Infinity
    let minY = Infinity,
      maxY = -Infinity

    visibleSeries.forEach((series) => {
      series.data.forEach((point) => {
        minX = Math.min(minX, point.wavelength)
        maxX = Math.max(maxX, point.wavelength)
        minY = Math.min(minY, point.value)
        maxY = Math.max(maxY, point.value)
      })
    })

    // 添加一些边距
    const xRange = maxX - minX
    const yRange = maxY - minY
    const xPadding = xRange * 0.05
    const yPadding = yRange * 0.05

    return {
      xAxis: {
        start: minX - xPadding,
        end: maxX + xPadding,
        step: Math.max(1, Math.round(xRange / 10)),
        min: minX - xPadding,
        max: maxX + xPadding
      },
      yAxis: {
        start: minY - yPadding,
        end: maxY + yPadding,
        step: Math.max(0.1, Math.round((yRange / 10) * 10) / 10),
        min: minY - yPadding,
        max: maxY + yPadding
      }
    }
  }

  // 切换曲线显示状态
  const toggleSeriesVisibility = (seriesId: string): void => {
    const updatedSeries = series.map((s) => (s.id === seriesId ? { ...s, visible: !s.visible } : s))
    setSeries(updatedSeries)

    // 计算可见曲线的坐标轴范围
    const visibleSeries = updatedSeries.filter((s) => s.visible)
    const axisRanges = calculateAxisRanges(visibleSeries)

    // 使用 dispatch 更新图表数据
    dispatch({ type: 'SET_CHART_DATA', payload: updatedSeries })

    // 更新坐标轴设置
    dispatch({ type: 'SET_X_AXIS', payload: axisRanges.xAxis })
    dispatch({ type: 'SET_Y_AXIS', payload: axisRanges.yAxis })
  }

  // 计算两条曲线的加法
  const calculateAddition = (): void => {
    if (!selectedSeriesA || !selectedSeriesB) {
      toast.error('请选择两条曲线进行计算')
      return
    }

    const seriesA = series.find((s) => s.id === selectedSeriesA)
    const seriesB = series.find((s) => s.id === selectedSeriesB)

    if (!seriesA || !seriesB) {
      toast.error('无法找到选中的曲线')
      return
    }

    // 确保两条曲线的波长范围相同
    if (seriesA.data.length !== seriesB.data.length) {
      toast.error('两条曲线的数据点数量不同，无法进行计算')
      return
    }

    // 创建新的数据点数组，计算 A + B
    const resultData: SpectralDataPoint[] = []

    // 对每个波长点进行计算
    for (let i = 0; i < seriesA.data.length; i++) {
      const pointA = seriesA.data[i]
      const pointB = seriesB.data.find((p) => p.wavelength === pointA.wavelength)

      if (pointB) {
        resultData.push({
          wavelength: pointA.wavelength,
          value: pointA.value + pointB.value
        })
      }
    }

    if (resultData.length === 0) {
      toast.error('两条曲线的波长点不匹配，无法进行计算')
      return
    }

    // 创建新的曲线
    const newSeries: SpectralSeries = {
      id: `calc_${Date.now()}`,
      name: `${seriesA.name} + ${seriesB.name}`,
      mode: seriesA.mode, // 使用第一条曲线的模式
      color: 'purple', // 使用新的颜色
      visible: true,
      data: resultData
    }

    // 添加新曲线到系列中
    const updatedSeries = [...series, newSeries]
    setSeries(updatedSeries)

    // 更新图表数据
    dispatch({ type: 'SET_CHART_DATA', payload: updatedSeries })

    // 重置选择
    setSelectedSeriesA('')
    setSelectedSeriesB('')

    toast.success('计算完成')
  }

  // 计算两条曲线的减法
  const calculateSubtraction = (): void => {
    if (!selectedSeriesA || !selectedSeriesB) {
      toast.error('请选择两条曲线进行计算')
      return
    }

    const seriesA = series.find((s) => s.id === selectedSeriesA)
    const seriesB = series.find((s) => s.id === selectedSeriesB)

    if (!seriesA || !seriesB) {
      toast.error('无法找到选中的曲线')
      return
    }

    // 确保两条曲线的波长范围相同
    if (seriesA.data.length !== seriesB.data.length) {
      toast.error('两条曲线的数据点数量不同，无法进行计算')
      return
    }

    // 创建新的数据点数组，计算 A - B
    const resultData: SpectralDataPoint[] = []

    // 对每个波长点进行计算
    for (let i = 0; i < seriesA.data.length; i++) {
      const pointA = seriesA.data[i]
      const pointB = seriesB.data.find((p) => p.wavelength === pointA.wavelength)

      if (pointB) {
        resultData.push({
          wavelength: pointA.wavelength,
          value: pointA.value - pointB.value
        })
      }
    }

    if (resultData.length === 0) {
      toast.error('两条曲线的波长点不匹配，无法进行计算')
      return
    }

    // 创建新的曲线
    const newSeries: SpectralSeries = {
      id: `calc_${Date.now()}`,
      name: `${seriesA.name} - ${seriesB.name}`,
      mode: seriesA.mode, // 使用第一条曲线的模式
      color: 'green', // 使用新的颜色
      visible: true,
      data: resultData
    }

    // 添加新曲线到系列中
    const updatedSeries = [...series, newSeries]
    setSeries(updatedSeries)

    // 更新图表数据
    dispatch({ type: 'SET_CHART_DATA', payload: updatedSeries })

    // 重置选择
    setSelectedSeriesA('')
    setSelectedSeriesB('')

    toast.success('计算完成')
  }

  return (
    <div className="flex flex-col gap-2 border rounded px-2 py-1">
      <div className="flex items-center gap-2">
        <div className="flex-1 flex items-center gap-2">

    
        <div className="relative pl-3 text-gray-500 ">{filePath || '请选择文件'}</div>
       
        {series.length > 0 && (
        <div>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm" className="whitespace-nowrap">
                <Settings className="h-4 w-4 mr-1" />
                图谱设置
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>图谱设置</DialogTitle>
              </DialogHeader>

              <div className="space-y-4 px-6">
                <div>
                  <h4 className="text-sm font-medium mb-3">曲线显示设置</h4>
                  <div className="space-y-2 max-h-60 overflow-y-auto">
                    {series.map((s) => (
                      <div key={s.id} className="flex items-center space-x-3 p-2 rounded-lg border">
                        <Checkbox
                          id={s.id}
                          checked={s.visible}
                          onCheckedChange={() => toggleSeriesVisibility(s.id)}
                        />
                        <div className="flex items-center space-x-2 flex-1">
                          <div
                            className="w-4 h-4 rounded-full border"
                            style={{ backgroundColor: s.color }}
                          />
                          <label
                            htmlFor={s.id}
                            className="text-sm font-medium cursor-pointer flex-1"
                          >
                            {s.name}
                          </label>
                          {s.visible ? (
                            <Eye className="h-4 w-4 text-green-600" />
                          ) : (
                            <EyeOff className="h-4 w-4 text-gray-400" />
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <h4 className="text-sm font-medium mb-3">光谱计算</h4>
                  <div className="space-y-3 p-3 border rounded-lg">
                    <div className="grid grid-cols-2 gap-2">
                      <div>
                        <label className="text-xs text-gray-500 mb-1 block">曲线 A</label>
                        <select
                          className="w-full rounded-md border border-gray-300 p-1 text-sm"
                          value={selectedSeriesA || ''}
                          onChange={(e) => setSelectedSeriesA(e.target.value)}
                        >
                          <option value="">请选择曲线</option>
                          {series.map((s) => (
                            <option key={s.id} value={s.id}>
                              {s.name}
                            </option>
                          ))}
                        </select>
                      </div>
                      <div>
                        <label className="text-xs text-gray-500 mb-1 block">曲线 B</label>
                        <select
                          className="w-full rounded-md border border-gray-300 p-1 text-sm"
                          value={selectedSeriesB || ''}
                          onChange={(e) => setSelectedSeriesB(e.target.value)}
                        >
                          <option value="">请选择曲线</option>
                          {series.map((s) => (
                            <option key={s.id} value={s.id}>
                              {s.name}
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>

                    <div className="flex justify-center space-x-3 mt-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={calculateAddition}
                        disabled={!selectedSeriesA || !selectedSeriesB}
                      >
                        A + B
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={calculateSubtraction}
                        disabled={!selectedSeriesA || !selectedSeriesB}
                      >
                        A - B
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
              <DialogFooter>
                <DialogClose asChild>
                  <Button onClick={() => setIsDialogOpen(false)} variant="outline">
                    关闭
                  </Button>
                </DialogClose>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      )}
          </div>
        <Button onClick={handleSelectFile} className="whitespace-nowrap" size="sm">
          <FolderOpen className="h-4 w-4 mr-2" />
          选择文件
        </Button>
      </div>

     
    </div>
  )
}

export default FilePath
