import React, { useEffect, useRef, useState, useMemo, useCallback } from 'react'

import * as echarts from 'echarts'
import { useChart } from '../contexts/ChartContext'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from './ui/dialog'
import { Input } from './ui/input'
import { Button } from './ui/button'

// 定义数据缩放参数接口
interface DataZoomParams {
  batch?: {
    startValue: number
    endValue: number
  }[]
}

// 定义标注点接口
interface Annotation {
  id: string
  x: number
  y: number
  text: string
  seriesName: string
  labelPosition?: [number, number] // 添加标签位置属性
}

const SpectralChart: React.FC = () => {
  const { state, dispatch } = useChart()
  const { activeMode: mode, chartData, xAxis, yAxis, feature2Data, fontSizes, activeTab } = state
  const containerRef = useRef<HTMLDivElement>(null)
  const chartRef = useRef<echarts.ECharts | null>(null)
  const chartInitializedRef = useRef<boolean>(false)
  const [currentTheme, setCurrentTheme] = useState<string>(
    document.documentElement.classList.contains('dark') ? 'dark' : 'light'
  )
  const [zoomEnabled, setZoomEnabled] = useState<boolean>(true)
  const latestStateRef = useRef(state)
  const [annotations, setAnnotations] = useState<Annotation[]>([])
  const [selectedPoint, setSelectedPoint] = useState<{
    x: number
    y: number
    seriesName: string
  } | null>(null)
  const [annotationText, setAnnotationText] = useState<string>('')
  const [dialogOpen, setDialogOpen] = useState<boolean>(false)
  const [nearbyAnnotations, setNearbyAnnotations] = useState<Annotation[]>([])
  const [contextMenuPosition, setContextMenuPosition] = useState<{ x: number; y: number }>({
    x: 0,
    y: 0
  })
  const [action, setAction] = useState<'add' | 'edit'>('add')
  const [contextMenuVisible, setContextMenuVisible] = useState<boolean>(false)
  const [editingAnnotation, setEditingAnnotation] = useState<Annotation | null>(null)

  // 监听主题变化
  useEffect(() => {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.attributeName === 'class') {
          const newTheme = document.documentElement.classList.contains('dark') ? 'dark' : 'light'
          if (newTheme !== currentTheme) {
            setCurrentTheme(newTheme)
          }
        }
      })
    })

    observer.observe(document.documentElement, { attributes: true })

    return () => {
      observer.disconnect()
    }
  }, [currentTheme])

  // 在每次渲染时更新 ref
  useEffect(() => {
    latestStateRef.current = state
  }, [state])

  // 监听chartData变化，重置用户标注
  useEffect(() => {
    // 当chartData发生变化时（通常是读取新文件时），清空所有用户标注
    setAnnotations([])
    setSelectedPoint(null)
    setAnnotationText('')
    setNearbyAnnotations([])
    setEditingAnnotation(null)
    setDialogOpen(false)
    setContextMenuVisible(false)
  }, [chartData])

  const getThemeBackground = (): string => {
    const theme = document.documentElement.classList.contains('dark') ? 'dark' : 'light'
    return theme === 'dark' ? '#171717' : '#fff'
  }

  // 获取当前主题下适合的网格线颜色
  const getGridLineColor = (): string => {
    const theme = document.documentElement.classList.contains('dark') ? 'dark' : 'light'
    return theme === 'dark' ? '#262626' : '#E5E7EB'
  }

  // 使用useMemo预处理数据，避免每次渲染时重复计算
  const processedSeries = useMemo(() => {
    const visibleSeries = chartData.filter((series) => series.visible)
    if (visibleSeries.length === 0) return []

    // 准备 ECharts 系列数据
    return visibleSeries.map((seriesData) => ({
      sampling: 'lttb',
      name: seriesData.name,
      type: 'line',
      data: seriesData.data.map((point) => [point.wavelength.toFixed(4), point.value.toFixed(4)]),
      lineStyle: {
        color: seriesData.color,
        width: seriesData.lineWidth || 2
      },
      itemStyle: {
        color: seriesData.color
      },
      label: {
        normal: {
          show: true,
          position: 'outside',
          formatter: '{c}'
        }
      },
      labelLayout: {
        draggable: true,
        // All labels are 20px to the top edge
        y: 20
      },
      labelLine: {
        // All series supports labelLine now.
        show: true,
        // length2 defines the second segement length of labelLine.
        length2: 5
      },
      symbol: 'circle', // 使用圆形数据点
      symbolSize: 10, // 设置数据点大小
      showSymbol: false, // 默认不显示数据点
      emphasis: {
        // 鼠标悬停时的效果
        scale: true, // 放大效果
        focus: 'series', // 聚焦整个系列
        itemStyle: {
          borderWidth: 2,
          borderColor: '#fff'
        }
      },
      smooth: false
    }))
  }, [chartData])

  // 处理Feature2标注数据
  const featureMarkers = useMemo(() => {
    const activeFeature2Row = feature2Data.find((row) => row.id === state.activeFeature2RowId)

    if (
      !activeFeature2Row ||
      !activeFeature2Row.centerWavelength ||
      !activeFeature2Row.wavelength1 ||
      !activeFeature2Row.wavelength2 ||
      !activeFeature2Row.maxTransmittance
    ) {
      return []
    }

    // 解析数据
    const centerWavelength = parseFloat(activeFeature2Row.centerWavelength)
    const wavelength1 = parseFloat(activeFeature2Row.wavelength1)
    const wavelength2 = parseFloat(activeFeature2Row.wavelength2)
    const maxTransmittance = parseFloat(activeFeature2Row.maxTransmittance)
    const halfMaxValue = maxTransmittance / 2

    if (
      isNaN(centerWavelength) ||
      isNaN(wavelength1) ||
      isNaN(wavelength2) ||
      isNaN(maxTransmittance)
    ) {
      return []
    }

    return [
      // 添加中心波长标注线
      {
        name: '中心波长',
        type: 'line',
        data: [
          [centerWavelength, 0],
          [centerWavelength, maxTransmittance]
        ],
        lineStyle: {
          color: '#ff4d4f',
          width: 1,
          type: 'dashed'
        },
        symbol: 'none',
        markPoint: {
          data: [
            {
              name: '中心波长点',
              coord: [centerWavelength, maxTransmittance],
              symbol: 'circle',
              symbolSize: 6,
              itemStyle: {
                color: '#ff4d4f'
              },
              label: {
                formatter: `(${centerWavelength.toFixed(2)}, ${maxTransmittance.toFixed(2)})`,
                color: currentTheme === 'dark' ? '#fff' : '#000',
                fontSize: fontSizes?.axisLabel || 10,
                position: 'top'
              }
            }
          ]
        },
        // 不显示在图例中
        legendHoverLink: false
      },
      // 添加50%Tmax波长1标注
      {
        name: '50%Tmax波长1',
        type: 'line',
        data: [
          [wavelength1, 0],
          [wavelength1, halfMaxValue]
        ],
        lineStyle: {
          color: '#1890ff',
          width: 1,
          type: 'dashed'
        },
        symbol: 'none',
        markPoint: {
          data: [
            {
              name: '波长1点',
              coord: [wavelength1, halfMaxValue],
              symbol: 'circle',
              symbolSize: 6,
              itemStyle: {
                color: '#1890ff'
              },
              label: {
                formatter: `(${wavelength1.toFixed(2)}, ${halfMaxValue.toFixed(2)})`,
                color: currentTheme === 'dark' ? '#fff' : '#000',
                fontSize: fontSizes?.axisLabel || 10,
                position: 'top'
              }
            }
          ]
        },
        // 不显示在图例中
        legendHoverLink: false
      },
      // 添加50%Tmax波长2标注
      {
        name: '50%Tmax波长2',
        type: 'line',
        data: [
          [wavelength2, 0],
          [wavelength2, halfMaxValue]
        ],
        lineStyle: {
          color: '#722ed1',
          width: 1,
          type: 'dashed'
        },
        symbol: 'none',
        markPoint: {
          data: [
            {
              name: '波长2点',
              coord: [wavelength2, halfMaxValue],
              symbol: 'circle',
              symbolSize: 6,
              itemStyle: {
                color: '#722ed1'
              },
              label: {
                formatter: `(${wavelength2.toFixed(2)}, ${halfMaxValue.toFixed(2)})`,
                color: currentTheme === 'dark' ? '#fff' : '#000',
                fontSize: fontSizes?.axisLabel || 10,
                position: 'top'
              }
            }
          ]
        },
        // 不显示在图例中
        legendHoverLink: false
      }
    ]
  }, [feature2Data, state.activeFeature2RowId, currentTheme, fontSizes])

  // 处理用户添加的标注点
  const annotationMarkers = useMemo(() => {
    if (annotations.length === 0) return []

    return annotations.map((annotation) => {
      // 增加标签距离，确保在最高点也能显示
      const labelDistance = 80 // 增加标签与点的距离

      // 根据Y轴位置智能选择角度方向
      // 获取当前Y轴范围来判断标注点位置
      const yAxisMid = yAxis?.start && yAxis?.end ? (yAxis.start + yAxis.end) / 2 : 50

      // 如果标注点在上半部分，标签向下偏移；在下半部分，标签向上偏移
      const isUpperHalf = annotation.y > yAxisMid
      const angle = isUpperHalf ? -Math.PI / 6 : Math.PI / 6 // 上半部分向下30度，下半部分向上30度

      // 计算标签位置 - 使用自定义位置或默认计算
      let labelX, labelY
      if (annotation.labelPosition) {
        ;[labelX, labelY] = annotation.labelPosition
      } else {
        labelX = annotation.x + labelDistance * Math.cos(Math.PI / 4) // 45度角向右
        labelY = annotation.y + labelDistance * Math.sin(angle) // 根据位置向上或向下
      }

      return {
        name: `标注-${annotation.id}`,
        type: 'scatter',
        data: [[annotation.x, annotation.y]],
        symbolSize: 10, // 更小的数据点，类似图片中的小点
        itemStyle: {
          color: '#6366f1' // 紫色
        },
        label: {
          show: true,
          formatter: annotation.text,
          position: [labelX - annotation.x, labelY - annotation.y], // 相对位置，在引导线末端
          fontSize: fontSizes?.axisLabel || 10,
          color: '#6366f1', // 紫色文本
          backgroundColor: 'transparent',
          align: 'left',
          verticalAlign: 'middle',
          padding: [0, 0, 0, 0],
          // 添加拖拽功能
          draggable: true,
          width: 60,
          height: 20,
          cursor: 'move', // 添加移动光标提示
          rich: {
            a: {
              width: 60,
              height: 20
            }
          }
        },
        labelLine: {
          show: true,
          lineStyle: {
            color: '#6366f1',
            width: 1,
            type: 'solid'
          },
          smooth: false,
          length: Math.sqrt(
            Math.pow(labelX - annotation.x, 2) + Math.pow(labelY - annotation.y, 2)
          ), // 动态计算长度
          length2: 0 // 无第二段
        },
        emphasis: {
          scale: false,
          itemStyle: {
            color: '#6366f1'
          }
        },
        z: 100, // 确保标注在其他系列之上
        legendHoverLink: false
      }
    })
  }, [annotations, fontSizes])

  // 预先计算图表配置
  const chartOption = useMemo(() => {
    // 即使没有数据，也返回一个有效的配置对象，而不是null
    const allSeries = [...processedSeries, ...featureMarkers, ...annotationMarkers]

    return {
      backgroundColor: getThemeBackground(),

      grid: {
        left: '5%',
        right: '5%',
        top: '20%',
        bottom: '15%',
        containLabel: false
      },
      // 启用缩放组件
      dataZoom: [
        {
          type: 'inside',
          xAxisIndex: 0,
          filterMode: 'none'
        },
        {
          type: 'inside',
          yAxisIndex: 0,
          filterMode: 'none'
        },
        {
          type: 'slider',
          xAxisIndex: 0,
          bottom: '5%',
          height: 20,
          show: zoomEnabled
        },
        {
          type: 'slider',
          yAxisIndex: 0,
          bottom: '5%',
          height: '90%',
          show: zoomEnabled
        }
      ],
      // 启用工具箱
      toolbox: {
        show: zoomEnabled,
        right: '5%',
        top: '5%',
        feature: {
          dataZoom: {
            realtime: false,
            icon: {
              back: 'image://images/null.png' // 设置一个不存在的图片路径，使图标不显示
            },
            brushStyle:{
              borderWidth:1,
              color:'rgba(255,36,36,0.1)',
              borderColor:'rgba(255,36,36,0.5)'
            }
          },
          myTool1: {
            // 自定义按钮
            show: true,
            title: '还原',
            icon: 'path://M192 631.3c-6.9 0-13.8-2.9-18.8-8.5l-67.7-76.7c-9.1-10.4-8.1-26.2 2.2-35.3 10.4-9.1 26.2-8.1 35.3 2.2l67.7 76.7c9.1 10.4 8.1 26.2-2.2 35.3-4.7 4.3-10.6 6.3-16.5 6.3z m7.3 5.3c-7 0-14-2.9-18.9-8.7-9-10.5-7.9-26.2 2.6-35.3l81.7-70.5c10.5-9 26.2-7.9 35.3 2.6s7.9 26.2-2.6 35.3l-81.7 70.5c-4.8 4.1-10.6 6.1-16.4 6.1z m333.1 273.2c-16.6 0-30-13.4-30-30s13.4-30 30-30c164.9 0 299.1-134.2 299.1-299.1S697.3 251.6 532.4 251.6c-164.9 0-299.1 134.2-299.1 299.1 0 16.6-13.4 30-30 30s-30-13.4-30-30c0-48.5 9.5-95.5 28.2-139.8 18.1-42.8 44-81.2 76.9-114.1 33-33 71.4-58.9 114.1-76.9 44.3-18.7 91.3-28.2 139.8-28.2s95.5 9.5 139.8 28.2c42.8 18.1 81.2 44 114.1 76.9 33 33 58.9 71.4 76.9 114.1 18.7 44.3 28.2 91.3 28.2 139.8 0 48.5-9.5 95.5-28.2 139.8-18.1 42.8-44 81.2-76.9 114.1-33 33-71.4 58.9-114.1 76.9-44.2 18.8-91.2 28.3-139.7 28.3z"',
            onclick: () => {
              dispatch({ type: 'RESET_X_AXIS' })
              dispatch({ type: 'RESET_Y_AXIS' })
            }
          }
          // ...点击执行相应的代码

          // restore: {},
          // saveAsImage: {}
        }
      },
      legend: {
        show: true,
        top: '5%',
        left: '5%',
        textStyle: {
          color: currentTheme === 'dark' ? '#fff' : '#000',
          fontSize: fontSizes?.legendText || 12
        },
        // 只显示原始数据系列，不显示标注线
        data: allSeries.filter(
          (i) =>
            !['中心波长', '50%Tmax波长1', '50%Tmax波长2'].includes(i.name) &&
            !i.name.startsWith('标注-')
        )
      },
      xAxis: {
        type: 'value',
        name: '波长 (nm)',
        nameLocation: 'middle',
        nameGap: 30,
        nameTextStyle: {
          color: currentTheme === 'dark' ? '#fff' : '#000',
          fontSize: fontSizes?.axisName || 12
        },
        axisLine: {
          lineStyle: {
            color: getGridLineColor()
          }
        },
        axisTick: {
          lineStyle: {
            color: getGridLineColor()
          },
          show: true
        },
        axisLabel: {
          color: currentTheme === 'dark' ? '#fff' : '#000',
          formatter: (value: number) => Math.round(value).toString(),
          fontSize: fontSizes?.axisLabel || 12
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: getGridLineColor(),
            width: 1
          }
        },
        min: xAxis?.start ?? undefined,
        max: xAxis?.end ?? undefined,
        interval: xAxis?.step == null ? undefined : xAxis.step
      },
      yAxis: {
        type: 'value',
        name: mode === 'T' ? '透过率 (%)' : mode === 'R' ? '反射率 (%)' : '吸光度',
        nameLocation: 'middle',
        nameGap: 50,
        nameTextStyle: {
          color: currentTheme === 'dark' ? '#fff' : '#000',
          fontSize: fontSizes?.axisName || 12
        },
        axisLine: {
          lineStyle: {
            color: getGridLineColor()
          }
        },
        axisTick: {
          show: true,
          lineStyle: {
            color: getGridLineColor()
          }
        },
        axisLabel: {
          color: currentTheme === 'dark' ? '#fff' : '#000',
          formatter: (value: number) => Math.round(value).toString(),
          fontSize: fontSizes?.axisLabel || 12
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: getGridLineColor(),
            width: 1
          }
        },
        min: yAxis?.start ?? undefined,
        max: yAxis?.end ?? undefined,
        interval: yAxis?.step == null ? undefined : yAxis.step
      },
      tooltip: {
        show: false,
        trigger: 'axis',
        backgroundColor: currentTheme === 'dark' ? '#262626' : '#fff',
        borderColor: getGridLineColor(),
        textStyle: {
          color: currentTheme === 'dark' ? '#fff' : '#000'
        },
        formatter: (params: any) => {
          // 检查是否是标注点
          if (params.seriesName && params.seriesName.startsWith('标注-')) {
            const annotation = annotations.find(
              (a) => a.id === params.seriesName.replace('标注-', '')
            )
            if (annotation) {
              return `<div>
                <div><strong>${annotation.text}</strong></div>
                <div>X: ${annotation.x.toFixed(2)}</div>
                <div>Y: ${annotation.y.toFixed(2)}</div>
                <div>系列: ${annotation.seriesName}</div>
              </div>`
            }
          }

          // 默认格式
          if (Array.isArray(params.value)) {
            return `<div>
              <div>X: ${parseFloat(params.value[0]).toFixed(2)}</div>
              <div>Y: ${parseFloat(params.value[1]).toFixed(2)}</div>
            </div>`
          }
          return `${params[0].value}`
        }
      },
      large: true,
      animation: false,
      series: allSeries
    }
  }, [
    processedSeries,
    featureMarkers,
    annotationMarkers,
    mode,
    xAxis,
    yAxis,
    currentTheme,
    zoomEnabled,
    fontSizes
  ])

  // 处理右键点击事件
  const handleContextMenu = (e: MouseEvent) => {
    if (!chartRef.current) return

    e.preventDefault()

    // 获取鼠标点击位置相对于图表容器的坐标
    const rect = containerRef.current!.getBoundingClientRect()
    const x = e.clientX - rect.left
    const y = e.clientY - rect.top

    // 将鼠标坐标转换为图表坐标
    const pointInPixel = [x, y]
    const pointInGrid = chartRef.current.convertFromPixel({ seriesIndex: 0 }, pointInPixel)

    if (pointInGrid) {
      const [xValue, yValue] = pointInGrid

      // 找到最接近的数据点和对应的系列
      let closestPoint = { x: xValue, y: yValue, seriesName: '' }
      let minDistance = Infinity

      processedSeries.forEach((series) => {
        series.data.forEach((point: any) => {
          if (point.length >= 2) {
            const px = parseFloat(point[0])
            const py = parseFloat(point[1])
            const distance = Math.sqrt(Math.pow(px - xValue, 2) + Math.pow(py - yValue, 2))

            if (distance < minDistance) {
              minDistance = distance
              closestPoint = { x: px, y: py, seriesName: series.name }
            }
          }
        })
      })

      // 查找附近的标注点
      const nearby = annotations.filter((annotation) => {
        const distance = Math.sqrt(
          Math.pow(annotation.x - xValue, 2) + Math.pow(annotation.y - yValue, 2)
        )
        // 增大检测范围，使标注更容易被选中
        return distance < 30
      })

      // 按距离排序，最近的在前面
      nearby.sort((a, b) => {
        const distanceA = Math.sqrt(Math.pow(a.x - xValue, 2) + Math.pow(a.y - yValue, 2))
        const distanceB = Math.sqrt(Math.pow(b.x - xValue, 2) + Math.pow(b.y - yValue, 2))
        return distanceA - distanceB
      })

      setNearbyAnnotations(nearby)
      setSelectedPoint(closestPoint)
      setContextMenuPosition({ x: e.clientX, y: e.clientY })
      setContextMenuVisible(true)
    }
  }

  // 处理点击其他地方关闭菜单
  const handleClickOutside = (e: MouseEvent) => {
    if (contextMenuVisible) {
      setContextMenuVisible(false)
    }
  }

  // 处理添加标注
  const handleAddAnnotation = () => {
    if (selectedPoint && annotationText.trim()) {
      // 确保坐标值为数字类型
      const x = parseFloat(selectedPoint.x.toString())
      const y = parseFloat(selectedPoint.y.toString())

      // 计算默认标签位置，与显示逻辑保持一致
      

      // 根据Y轴位置智能选择角度方向
      const yAxisMid = yAxis?.start && yAxis?.end ? (yAxis.start + yAxis.end) / 2 : 50

      // 如果标注点在上半部分，标签向下偏移；在下半部分，标签向上偏移
      const isUpperHalf = y > yAxisMid
      const labelDistance = !isUpperHalf ? 80 : 120 // 增加标签与点的距离
      const angle = Math.PI // 上半部分向下30度，下半部分向上30度

      const labelX = x + labelDistance * Math.cos(angle) // 45度角向右
      const labelY = y + labelDistance * Math.sin(angle) // 根据位置向上或向下

      const newAnnotation: Annotation = {
        id: Date.now().toString(),
        x,
        y,
        text: annotationText,
        seriesName: selectedPoint.seriesName,
        labelPosition: [labelX, labelY] // 初始化标签位置
      }

      setAnnotations((prev) => [...prev, newAnnotation])
      setDialogOpen(false)
      setAnnotationText('')
    }
  }

  // 处理编辑标注
  const handleEditAnnotation = () => {
    if (nearbyAnnotations.length > 0 && annotationText.trim()) {
      const annotationToEdit = nearbyAnnotations[0]

      setAnnotations((prev) =>
        prev.map((annotation) =>
          annotation.id === annotationToEdit.id
            ? { ...annotation, text: annotationText }
            : annotation
        )
      )

      setDialogOpen(false)
      setAnnotationText('')
      setEditingAnnotation(null)
    }
  }

  // 处理删除标注 - 直接删除最近的标注
  const handleDeleteAnnotation = () => {
    if (nearbyAnnotations.length > 0) {
      // 获取最近的标注（已按距离排序）
      const closestAnnotation = nearbyAnnotations[0]
      setAnnotations((prev) => prev.filter((annotation) => annotation.id !== closestAnnotation.id))
      setContextMenuVisible(false)
    }
  }

  // 保存标注位置 - 优化版本，避免不必要的状态更新
  const saveAnnotationPositions = useCallback(() => {
    if (!chartRef.current || annotations.length === 0) return

    // 获取当前图表中的所有系列
    const option = chartRef.current.getOption() as echarts.EChartsOption
    const series = option.series as any[]

    if (!series) return

    let hasChanges = false
    const updatedAnnotations: Annotation[] = []

    // 遍历所有标注系列
    series.forEach((s) => {
      if (s.name && s.name.startsWith('标注-')) {
        const annotationId = s.name.replace('标注-', '')
        const annotation = annotations.find((a) => a.id === annotationId)

        if (annotation && s.label && s.label.position) {
          // 获取当前标签位置
          const labelPos = s.label.position

          if (Array.isArray(labelPos) && labelPos.length === 2) {
            // 计算绝对位置
            const absX = annotation.x + labelPos[0]
            const absY = annotation.y + labelPos[1]

            // 检查位置是否真的发生了变化
            const currentLabelPos = annotation.labelPosition
            const positionChanged = !currentLabelPos ||
              Math.abs(currentLabelPos[0] - absX) > 0.1 ||
              Math.abs(currentLabelPos[1] - absY) > 0.1

            if (positionChanged) {
              hasChanges = true
              updatedAnnotations.push({
                ...annotation,
                labelPosition: [absX, absY]
              })
            } else {
              updatedAnnotations.push(annotation)
            }
          } else {
            updatedAnnotations.push(annotation)
          }
        }
      }
    })

    // 只在位置真正发生变化时才更新状态
    if (hasChanges) {
      setAnnotations(updatedAnnotations)
    }
  }, [annotations])

  // 使用ref跟踪上一次的主题状态
  const prevThemeRef = useRef(currentTheme)

  // 只在主题变化时保存标注位置，避免annotations变化导致的无限循环
  useEffect(() => {
    if (prevThemeRef.current !== currentTheme) {
      saveAnnotationPositions()
      prevThemeRef.current = currentTheme
    }
  }, [currentTheme])

  // 创建并渲染图表
  useEffect(() => {
    if (!containerRef.current) return

    // 如果图表尚未初始化，则创建新实例
    if (!chartRef.current) {
      chartRef.current = echarts.init(containerRef.current, currentTheme === 'dark' ? 'dark' : null)
      chartInitializedRef.current = true

      // 监听缩放事件
      chartRef.current.on('dataZoom', function (this: echarts.ECharts, params: unknown) {
        console.log(params)
        const dataZoomParams = params as DataZoomParams
        if (dataZoomParams.batch && dataZoomParams.batch.length === 2) {
          const startValue = dataZoomParams.batch[0].startValue
          const endValue = dataZoomParams.batch[0].endValue

          // 更新X轴设置到侧边栏
          dispatch({
            type: 'SET_X_AXIS',
            payload: { start: startValue, end: endValue, step: null }
          })
          const startYValue = dataZoomParams.batch[1].startValue
          const endYValue = dataZoomParams.batch[1].endValue
          // 更新Y轴设置到侧边栏
          dispatch({
            type: 'SET_Y_AXIS',
            payload: { start: startYValue, end: endYValue, step: null }
          })
          // 获取最新状态
          const currentState = latestStateRef.current

          if (currentState.activeTab === 'feature1') {
            // 更新功能1的波长起点和终点
            if (currentState.feature1Data.length > 0) {
              const lastRowId = currentState.feature1Data[currentState.feature1Data.length - 1].id

              dispatch({
                type: 'UPDATE_FEATURE1_ROW',
                payload: { id: lastRowId, field: 'startWavelength', value: String(startValue) }
              })
              dispatch({
                type: 'UPDATE_FEATURE1_ROW',
                payload: { id: lastRowId, field: 'endWavelength', value: String(endValue) }
              })

              // 计算该范围内的最大值、最小值、平均值
              dispatch({ type: 'CALCULATE_ALL_FEATURE1_VALUES' })
            }
          } else if (currentState.activeTab === 'feature2') {
            // 更新功能2的波长起点和终点
            if (currentState.feature2Data.length > 0 && currentState.activeFeature2RowId) {
              dispatch({
                type: 'UPDATE_FEATURE2_ROW',
                payload: {
                  id: currentState.activeFeature2RowId,
                  field: 'startWavelength',
                  value: String(startValue)
                }
              })
              dispatch({
                type: 'UPDATE_FEATURE2_ROW',
                payload: {
                  id: currentState.activeFeature2RowId,
                  field: 'endWavelength',
                  value: String(endValue)
                }
              })

              // 计算该范围内的中心波长等数据
              dispatch({
                type: 'CALCULATE_FEATURE2_VALUES',
                payload: currentState.activeFeature2RowId
              })
            }
          }
        }
      })

      // 监听标签拖拽事件
      chartRef.current.on('labelDragging', function (params: any) {
        // 获取当前拖拽的标签所属的系列和数据点索引
        const { seriesIndex } = params

        // 只处理标注系列
        if (chartRef.current) {
          const option = chartRef.current.getOption() as echarts.EChartsOption
          const series = option.series as any[]
          if (series && series[seriesIndex] && series[seriesIndex].name.startsWith('标注-')) {
            // 更新标签位置
            console.log('Label dragging:', params)
          }
        }
      })

      // 监听标签拖拽结束事件
      chartRef.current.on('labelDragEnd', function (params: any) {
        // 获取当前拖拽的标签所属的系列和数据点索引
        const { seriesIndex, x, y } = params

        // 只处理标注系列
        if (chartRef.current) {
          const option = chartRef.current.getOption() as echarts.EChartsOption
          const series = option.series as any[]

          if (series && series[seriesIndex] && series[seriesIndex].name.startsWith('标注-')) {
            const seriesName = series[seriesIndex].name
            const annotationId = seriesName.replace('标注-', '')

            // 获取当前数据点的坐标
            const dataCoord = chartRef.current.convertFromPixel({ seriesIndex }, [x, y])

            // 更新标注的标签位置
            setAnnotations((prev) =>
              prev.map((anno) => {
                if (anno.id === annotationId) {
                  return {
                    ...anno,
                    labelPosition: dataCoord as [number, number]
                  }
                }
                return anno
              })
            )
          }
        }
      })

      window['echartsInstance'] = chartRef.current
    }

    // 添加右键菜单事件监听
    const currentContainer = containerRef.current
    currentContainer.addEventListener('contextmenu', handleContextMenu)

    // 添加全局点击事件监听器，用于关闭右键菜单
    document.addEventListener('click', handleClickOutside as EventListener)

    // 设置图表选项 - 始终设置选项，即使是空图表
    chartRef.current.setOption(chartOption, true)

    // 如果有数据，添加十字线功能
    if (processedSeries.length > 0) {
      chartRef.current.dispatchAction({
        type: 'showTip',
        seriesIndex: 0,
        dataIndex: 0
      })
    }

    // 响应式窗口缩放
    const handleResize = (): void => {
      if (chartRef.current) {
        chartRef.current.resize()
      }
    }

    window.addEventListener('resize', handleResize)

    // 清理事件监听器
    return () => {
      window.removeEventListener('resize', handleResize)
      document.removeEventListener('click', handleClickOutside as EventListener)
      if (currentContainer) {
        currentContainer.removeEventListener('contextmenu', handleContextMenu)
      }
    }
  }, [
    chartOption,
    processedSeries.length,
    dispatch,
    latestStateRef.current.feature1Data,
    currentTheme
  ])

  // 组件卸载时清理ECharts实例
  useEffect(() => {
    return () => {
      if (chartRef.current) {
        // 移除所有事件监听器
        chartRef.current.off('dataZoom')
        chartRef.current.off('labelDragging')
        chartRef.current.off('labelDragEnd')

        // 销毁图表实例
        chartRef.current.dispose()
        chartRef.current = null
        chartInitializedRef.current = false

        // 清理全局引用
        if (window['echartsInstance']) {
          delete window['echartsInstance']
        }
      }
    }
  }, [])

  return (
    <div className="w-full h-full relative p-1">
      <div className="w-full h-full" ref={containerRef}></div>

      {/* 自定义右键菜单 */}
      {contextMenuVisible && selectedPoint && (
        <div
          className="fixed bg-popover text-popover-foreground z-50 min-w-[8rem] overflow-hidden rounded-md border p-1 shadow-md"
          style={{
            left: `${contextMenuPosition.x}px`,
            top: `${contextMenuPosition.y}px`
          }}
        >
          {/* 添加标注 */}
          {nearbyAnnotations.length === 0 && (
            <button
              className="relative flex w-full cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
              onClick={() => {
                setAction('add')
                setDialogOpen(true)
                setContextMenuVisible(false)
              }}
            >
              添加标注
            </button>
          )}
          {nearbyAnnotations.length > 0 && (
            <>
              <button
                className="relative flex w-full cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
                onClick={() => {
                  setAction('edit')
                  const annotationToEdit = nearbyAnnotations[0]
                  setEditingAnnotation(annotationToEdit)
                  setAnnotationText(annotationToEdit.text)
                  setDialogOpen(true)
                  setContextMenuVisible(false)
                }}
              >
                编辑标注
              </button>
              <div className="h-px my-1 -mx-1 bg-border" />
              <button
                className="relative flex w-full cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none text-destructive hover:bg-destructive/10 focus:bg-destructive/10 focus:text-destructive"
                onClick={() => {
                  handleDeleteAnnotation()
                }}
              >
                删除标注
              </button>
            </>
          )}
        </div>
      )}

      {/* 添加/编辑标注对话框 */}
      <Dialog
        open={dialogOpen}
        onOpenChange={(open) => {
          if (!open) {
            setDialogOpen(false)
            setEditingAnnotation(null)
          }
        }}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{action === 'add' ? '添加标注' : '编辑标注'}</DialogTitle>
          </DialogHeader>
          <div className="py-4 px-6">
            <div className="mb-4">
              {action === 'add' ? (
                <>
                  <p className="text-sm font-medium mb-1">坐标信息</p>
                  <div className="grid grid-cols-2 gap-2 mb-2">
                    <div className="flex items-center">
                      <span className="text-sm text-gray-500 dark:text-gray-400 mr-2">X:</span>
                      <span className="text-sm font-medium">{selectedPoint?.x?.toFixed(4)}</span>
                    </div>
                    <div className="flex items-center">
                      <span className="text-sm text-gray-500 dark:text-gray-400 mr-2">Y:</span>
                      <span className="text-sm font-medium">{selectedPoint?.y?.toFixed(4)}</span>
                    </div>
                  </div>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mb-4">
                    系列: {selectedPoint?.seriesName}
                  </p>
                </>
              ) : (
                <>
                  <p className="text-sm font-medium mb-1">坐标信息</p>
                  <div className="grid grid-cols-2 gap-2 mb-2">
                    <div className="flex items-center">
                      <span className="text-sm text-gray-500 dark:text-gray-400 mr-2">X:</span>
                      <span className="text-sm font-medium">
                        {editingAnnotation?.x?.toFixed(4)}
                      </span>
                    </div>
                    <div className="flex items-center">
                      <span className="text-sm text-gray-500 dark:text-gray-400 mr-2">Y:</span>
                      <span className="text-sm font-medium">
                        {editingAnnotation?.y?.toFixed(4)}
                      </span>
                    </div>
                  </div>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mb-4">
                    系列: {editingAnnotation?.seriesName}
                  </p>
                </>
              )}
            </div>
            <div>
              <p className="text-sm font-medium mb-2">标注文本</p>
              <Input
                placeholder="输入标注文本"
                value={annotationText}
                onChange={(e) => setAnnotationText(e.target.value)}
                autoFocus
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={action === 'add' ? handleAddAnnotation : handleEditAnnotation}>
              确认
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default SpectralChart
