import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>bsTrigger } from './ui/tabs'
import { Separator } from './ui/separator'
import { Input } from './ui/input'
import { But<PERSON> } from './ui/button'
import { Card, CardContent, CardHeader, CardTitle } from './ui/card'
import { Label } from './ui/label'
import {
  Settings,
  ChevronRight,
  BarChart3,
  LineChart,
  Palette,
  ZoomIn,
  RotateCcw,
  Type,
  Tag
} from 'lucide-react'
import { useChart } from '../contexts/ChartContext'
import { Hue, IColor, Saturation, Alpha, useColor } from 'react-color-palette'
import 'react-color-palette/css'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select'

const ControlPanel: React.FC = () => {
  const { state, dispatch } = useChart()
  const [selectedColor, setSelectedColor] = useColor('#FF2828')
  const [selectedSeriesId, setSelectedSeriesId] = useState<string | null>(
    state.chartData.length > 0 ? state.chartData[0].id : null
  )
  const [legendName, setLegendName] = useState<string>(
    selectedSeriesId && state.chartData.find(s => s.id === selectedSeriesId)
      ? state.chartData.find(s => s.id === selectedSeriesId)!.name
      : ''
  )
  const [lineWidth, setLineWidth] = useState<number>(
    selectedSeriesId && state.chartData.find(s => s.id === selectedSeriesId)
      ? state.chartData.find(s => s.id === selectedSeriesId)!.lineWidth || 2
      : 2
  )

  // 坐标轴设置状态
  const [xAxisSettings, setXAxisSettings] = useState({
    start: state.xAxis?.start?.toString() || '',
    end: state.xAxis?.end?.toString() || '',
    step: state.xAxis?.step?.toString() || '',
    min: state.xAxis?.min || 0,
    max: state.xAxis?.max || 1000
  })

  const [yAxisSettings, setYAxisSettings] = useState({
    start: state.yAxis?.start?.toString() || '',
    end: state.yAxis?.end?.toString() || '',
    step: state.yAxis?.step?.toString() || '',
    min: state.yAxis?.min || 0,
    max: state.yAxis?.max || 1000
  })

  // 字体大小设置
  const [fontSettings, setFontSettings] = useState({
    axisLabel: 12,
    axisName: 12,
    legendText: 12
  })

  // 缩放控制状态
  const [zoomEnabled, setZoomEnabled] = useState(false)

  // 当图表状态中的坐标轴设置变化时更新本地状态
  useEffect(() => {
    setXAxisSettings({
      start: state.xAxis?.start?.toString() || '',
      end: state.xAxis?.end?.toString() || '',
      step: state.xAxis?.step?.toString() || '',
      min: state.xAxis?.min || 0,
      max: state.xAxis?.max || 1000
    })

    setYAxisSettings({
      start: state.yAxis?.start?.toString() || '',
      end: state.yAxis?.end?.toString() || '',
      step: state.yAxis?.step?.toString() || '',
      min: state.yAxis?.min || 0,
      max: state.yAxis?.max || 1000
    })
  }, [state.xAxis, state.yAxis])

  // 当选中的系列变化时更新图例名称和线条粗细
  useEffect(() => {
    if (selectedSeriesId) {
      const selectedSeries = state.chartData.find(s => s.id === selectedSeriesId)
      if (selectedSeries) {
        setLegendName(selectedSeries.name)
        setLineWidth(selectedSeries.lineWidth || 2)
      }
    }
  }, [selectedSeriesId, state.chartData])

  // 处理X轴输入变化
  const handleXAxisChange = (field: 'start' | 'end' | 'step', value: string) => {
    setXAxisSettings((prev) => ({
      ...prev,
      [field]: value
    }))
  }

  // 处理Y轴输入变化
  const handleYAxisChange = (field: 'start' | 'end' | 'step', value: string) => {
    setYAxisSettings((prev) => ({
      ...prev,
      [field]: value
    }))
  }

  // 处理字体大小变化
  const handleFontSizeChange = (field: 'axisLabel' | 'axisName' | 'legendText', value: string) => {
    const numValue = parseInt(value) || 12
    setFontSettings(prev => ({
      ...prev,
      [field]: numValue
    }))
  }

  // 应用字体大小设置
  const applyFontSettings = () => {
    dispatch({
      type: 'SET_FONT_SIZES',
      payload: fontSettings
    })
  }

  // 应用X轴设置
  const applyXAxisSettings = () => {
    const start = xAxisSettings.start === '' ? null : parseFloat(xAxisSettings.start)
    const end = xAxisSettings.end === '' ? null : parseFloat(xAxisSettings.end)
    const step = xAxisSettings.step === '' ? null : parseFloat(xAxisSettings.step)

    dispatch({
      type: 'SET_X_AXIS',
      payload: { start, end, step }
    })
  }

  // 应用Y轴设置
  const applyYAxisSettings = () => {
    const start = yAxisSettings.start === '' ? null : parseFloat(yAxisSettings.start)
    const end = yAxisSettings.end === '' ? null : parseFloat(yAxisSettings.end)
    const step = yAxisSettings.step === '' ? null : parseFloat(yAxisSettings.step)

    dispatch({
      type: 'SET_Y_AXIS',
      payload: { start, end, step }
    })
  }
  // 重置Y轴设置
  const resetYAxisSettings = () => {
    dispatch({
      type: 'RESET_Y_AXIS'
    })
  }

  // 处理颜色变更
  const handleColorChangeComplete = (color: IColor) => {
    setSelectedColor(color)

    // 如果有选中的系列，更新其颜色
    if (selectedSeriesId) {
      dispatch({
        type: 'SET_CHART_COLOR',
        payload: {
          seriesId: selectedSeriesId,
          color: color.hex
        }
      })
    }
  }

  // 应用图例名称更改
  const applyLegendNameChange = () => {
    if (selectedSeriesId && legendName.trim() !== '') {
      dispatch({
        type: 'SET_SERIES_NAME',
        payload: {
          seriesId: selectedSeriesId,
          name: legendName
        }
      })
    }
  }

  // 应用线条粗细更改
  const applyLineWidthChange = () => {
    if (selectedSeriesId && lineWidth >= 1 && lineWidth <= 10) {
      dispatch({
        type: 'SET_SERIES_LINE_WIDTH',
        payload: {
          seriesId: selectedSeriesId,
          lineWidth: lineWidth
        }
      })
    }
  }

  return (
    <div className="flex flex-col flex-1 space-y-4">
      <Tabs defaultValue="axes" className="w-full">
        <TabsList className="grid w-full grid-cols-3 h-9">
        <TabsTrigger value="axes">
            <LineChart className="h-3.5 w-3.5 mr-1.5" />
            <span className="text-xs font-medium">坐标轴</span>
          </TabsTrigger>
          <TabsTrigger value="series">
            <Tag className="h-3.5 w-3.5 mr-1.5" />
            <span className="text-xs font-medium">系列</span>
          </TabsTrigger>
         
          <TabsTrigger value="font">
            <Type className="h-3.5 w-3.5 mr-1.5" />
            <span className="text-xs font-medium">字体</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="series" className="space-y-3 mt-3">
          <div className="space-y-3">
            <div className="space-y-1.5">
              <Label className="text-xs font-medium">选择系列</Label>
              <Select 
                value={selectedSeriesId || ''} 
                onValueChange={(value) => setSelectedSeriesId(value)}
              >
                <SelectTrigger className="h-8">
                  <SelectValue placeholder="选择系列" />
                </SelectTrigger>
                <SelectContent>
                  {state.chartData.map((series) => (
                    <SelectItem key={series.id} value={series.id}>
                      {series.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-1.5">
              <Label className="text-xs font-medium">图例名称</Label>
              <div className="flex items-center space-x-2">
                <Input
                  className="h-8"
                  placeholder="图例名称"
                  value={legendName}
                  onChange={(e) => setLegendName(e.target.value)}
                />
                <Button size="sm" className="h-8" onClick={applyLegendNameChange}>
                  应用
                </Button>
              </div>
            </div>

            <div className="space-y-1.5">
              <Label className="text-xs font-medium">线条粗细</Label>
              <div className="flex items-center space-x-2">
                <Input
                  type="number"
                  className="h-8"
                  placeholder="线条粗细"
                  min={1}
                  max={10}
                  value={lineWidth}
                  onChange={(e) => setLineWidth(parseInt(e.target.value) || 2)}
                />
                <Button size="sm" className="h-8" onClick={applyLineWidthChange}>
                  应用
                </Button>
              </div>
            </div>

            <div className="space-y-1.5">
              <Label className="text-xs font-medium">系列颜色</Label>
              <Saturation
                height={150}
                color={selectedColor}
                onChange={setSelectedColor}
                onChangeComplete={handleColorChangeComplete}
              />
             
                <Alpha
                  color={selectedColor}
                  onChange={setSelectedColor}
                  onChangeComplete={handleColorChangeComplete}
                />
                <Hue
                  color={selectedColor}
                  onChange={setSelectedColor}
                  onChangeComplete={handleColorChangeComplete}
                />
           
            </div>
          </div>
        </TabsContent>

        <TabsContent value="axes" className="space-y-3 mt-3">
          <Tabs defaultValue="x-axis" className="w-full">
            <TabsList className="grid w-full grid-cols-2 h-8">
              <TabsTrigger value="x-axis" className="text-xs">
                横坐标
              </TabsTrigger>
              <TabsTrigger value="y-axis" className="text-xs">
                纵坐标
              </TabsTrigger>
            </TabsList>

            <TabsContent value="x-axis" className="space-y-3 mt-3">
              <div className="grid grid-cols-2 gap-3">
                <div className="space-y-1.5">
                  <Label className="text-xs font-medium">起始值</Label>
                  <Input
                    type="number"
                    className="h-8"
                    min={xAxisSettings.min}
                    placeholder="起始值"
                    value={xAxisSettings.start}
                    onChange={(e) => handleXAxisChange('start', e.target.value)}
                  />
                </div>
                <div className="space-y-1.5">
                  <Label className="text-xs font-medium">终点值</Label>
                  <Input
                    type="number"
                    className="h-8"
                    placeholder="终点值"
                    value={xAxisSettings.end}
                    onChange={(e) => handleXAxisChange('end', e.target.value)}
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-3">
                <div className="space-y-1.5">
                  <Label className="text-xs font-medium">刻度尺</Label>
                  <Input
                    type="number"
                    className="h-8"
                    placeholder="刻度尺"
                    value={xAxisSettings.step}
                    onChange={(e) => handleXAxisChange('step', e.target.value)}
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-3">
                <Button size="sm" className="w-full h-8" type="submit" onClick={applyXAxisSettings}>
                  应用
                  <ChevronRight className="h-3.5 w-3.5 ml-1" />
                </Button>
                <Button
                  size="sm"
                  className="w-full h-8"
                  variant="secondary"
                  onClick={() => {
                    dispatch({
                      type: 'RESET_X_AXIS'
                    })
                  }}
                >
                  {' '}
                  重置 <RotateCcw className="h-3.5 w-3.5 ml-1" />
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="y-axis" className="space-y-3 mt-3">
              <div className="grid grid-cols-2 gap-3">
                <div className="space-y-1.5">
                  <Label className="text-xs font-medium">起始值</Label>
                  <Input
                    type="number"
                    className="h-8"
                    placeholder="起始值"
                    value={yAxisSettings.start}
                    onChange={(e) => handleYAxisChange('start', e.target.value)}
                  />
                </div>
                <div className="space-y-1.5">
                  <Label className="text-xs font-medium">终点值</Label>
                  <Input
                    type="number"
                    className="h-8"
                    placeholder="终点值"
                    value={yAxisSettings.end}
                    onChange={(e) => handleYAxisChange('end', e.target.value)}
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-3">
                <div className="space-y-1.5">
                  <Label className="text-xs font-medium">刻度尺</Label>
                  <Input
                    type="number"
                    className="h-8"
                    placeholder="刻度尺"
                    value={yAxisSettings.step}
                    onChange={(e) => handleYAxisChange('step', e.target.value)}
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-3">
                <Button size="sm" className="w-full h-8" onClick={applyYAxisSettings}>
                  应用
                  <ChevronRight className="h-3.5 w-3.5 ml-1" />
                </Button>
                <Button
                  size="sm"
                  className="w-full h-8"
                  variant="secondary"
                  onClick={() => {
                    dispatch({
                      type: 'RESET_Y_AXIS'
                    })
                  }}
                >
                  重置
                  <RotateCcw className="h-3.5 w-3.5 ml-1" />
                </Button>
              </div>
            </TabsContent>
          </Tabs>
        </TabsContent>

        <TabsContent value="font" className="space-y-3 mt-3">
          <div className="space-y-3">
            <div className="space-y-1.5">
              <Label className="text-xs font-medium">坐标轴标签字体大小</Label>
              <Input
                type="number"
                className="h-8"
                min={8}
                max={24}
                value={fontSettings.axisLabel}
                onChange={(e) => handleFontSizeChange('axisLabel', e.target.value)}
              />
            </div>
            <div className="space-y-1.5">
              <Label className="text-xs font-medium">坐标轴名称字体大小</Label>
              <Input
                type="number"
                className="h-8"
                min={8}
                max={24}
                value={fontSettings.axisName}
                onChange={(e) => handleFontSizeChange('axisName', e.target.value)}
              />
            </div>
            <div className="space-y-1.5">
              <Label className="text-xs font-medium">图例字体大小</Label>
              <Input
                type="number"
                className="h-8"
                min={8}
                max={24}
                value={fontSettings.legendText}
                onChange={(e) => handleFontSizeChange('legendText', e.target.value)}
              />
            </div>
            <Button size="sm" className="w-full h-8" onClick={applyFontSettings}>
              应用字体设置
              <ChevronRight className="h-3.5 w-3.5 ml-1" />
            </Button>
          </div>
        </TabsContent>
      </Tabs>

      <Separator />
    </div>
  )
}

export default ControlPanel
