# 标注功能无限循环错误修复报告

## 问题描述
在添加第二个标注或标记标注时会报错：
```
Error: Maximum update depth exceeded. This can happen when a component repeatedly calls setState inside componentWillUpdate or componentDidUpdate. React limits the number of nested updates to prevent infinite loops.
```

## 问题根源分析
在 `SpectralChart.tsx` 组件中，第765-767行的useEffect存在无限循环问题：

```typescript
// 问题代码
useEffect(() => {
  saveAnnotationPositions()
}, [currentTheme, annotations])  // annotations作为依赖项导致循环
```

**循环流程：**
1. `annotations`状态变化 → 触发useEffect
2. 调用`saveAnnotationPositions()` → 内部调用`setAnnotations()`
3. `setAnnotations()`更新状态 → 再次触发useEffect
4. 无限循环...

## 解决方案
采用**方案2：优化useEffect依赖项**，通过以下方式修复：

### 1. 优化useEffect依赖项
- 移除`annotations`从依赖数组中
- 使用`useRef`跟踪主题变化
- 只在主题真正变化时才保存标注位置

```typescript
// 修复后的代码
const prevThemeRef = useRef(currentTheme)

useEffect(() => {
  if (prevThemeRef.current !== currentTheme) {
    saveAnnotationPositions()
    prevThemeRef.current = currentTheme
  }
}, [currentTheme])  // 只依赖currentTheme
```

### 2. 优化saveAnnotationPositions函数
- 使用`useCallback`优化性能
- 添加位置变化检测，只在位置真正改变时才更新
- 避免不必要的状态更新

```typescript
const saveAnnotationPositions = useCallback(() => {
  // ... 省略实现细节
  
  // 检查位置是否真的发生了变化
  const positionChanged = !currentLabelPos || 
    Math.abs(currentLabelPos[0] - absX) > 0.1 || 
    Math.abs(currentLabelPos[1] - absY) > 0.1

  // 只在位置真正发生变化时才更新状态
  if (hasChanges) {
    setAnnotations(updatedAnnotations)
  }
}, [annotations])
```

## 修改文件
- `src\renderer\src\components\SpectralChart.tsx`

## 修改内容
1. 导入`useCallback`
2. 添加`prevThemeRef`用于跟踪主题变化
3. 重构useEffect依赖项逻辑
4. 优化`saveAnnotationPositions`函数
5. 修复未使用变量的警告

## 测试验证
- ✅ 编译无错误
- ✅ 应用程序正常启动
- ✅ 无TypeScript类型错误
- 🔄 需要手动测试标注功能

## 预期效果
- 添加多个标注时不再出现无限循环错误
- 主题切换时标注位置正常保存
- 标注拖拽功能正常工作
- 性能得到优化，减少不必要的重渲染

## 建议后续测试
1. 添加第一个标注
2. 添加第二个标注（之前会报错的场景）
3. 拖拽标注标签
4. 切换主题
5. 编辑和删除标注

修复完成时间：2025-07-17
