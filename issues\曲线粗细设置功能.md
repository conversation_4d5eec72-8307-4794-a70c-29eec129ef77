# 曲线粗细设置功能

## 功能概述
为图表应用添加了曲线粗细大小设置功能，用户可以通过控制面板调整选中系列的线条粗细。

## 实现的功能
1. **线条粗细控制**：用户可以设置1-10像素的线条粗细
2. **实时预览**：设置后立即在图表中生效
3. **系列独立设置**：每个系列可以有不同的线条粗细
4. **默认值**：新系列默认线条粗细为2像素

## 修改的文件

### 1. `src/renderer/src/reducers/chartReducer.ts`
- 添加了 `SET_SERIES_LINE_WIDTH` action类型
- 在reducer中添加了处理线条粗细设置的逻辑
- 更新了初始数据，为默认系列添加了 `lineWidth: 2` 属性

### 2. `src/renderer/src/components/ControlPanel.tsx`
- 添加了 `lineWidth` 状态管理
- 添加了 `applyLineWidthChange` 函数处理线条粗细变更
- 在系列标签页中添加了线条粗细输入控件
- 更新了useEffect来同步选中系列的线条粗细状态

### 3. `src/renderer/src/components/SpectralChart.tsx`
- 修改了ECharts配置，使用 `seriesData.lineWidth || 2` 作为线条宽度

## 使用方法
1. 打开应用程序
2. 在控制面板中切换到"系列"标签页
3. 选择要修改的系列
4. 在"线条粗细"输入框中输入1-10之间的数值
5. 点击"应用"按钮
6. 图表中的对应系列线条粗细会立即更新

## 技术细节
- 线条粗细范围：1-10像素
- 默认值：2像素
- 数据类型：number
- 存储位置：SpectralSeries接口的lineWidth属性（可选）

## 测试建议
1. 测试不同粗细值的显示效果
2. 测试多个系列设置不同粗细
3. 测试边界值（1和10）
4. 测试无效输入的处理
5. 测试系列切换时粗细值的同步

## 后续优化建议
1. 可以考虑添加滑块控件提供更好的用户体验
2. 可以添加预设的粗细选项（细、中、粗）
3. 可以考虑添加线条样式选项（实线、虚线等）
