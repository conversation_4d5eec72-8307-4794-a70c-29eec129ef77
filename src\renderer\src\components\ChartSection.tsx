import React, { useState, useCallback } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from './ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from './ui/tabs'
import Spectral<PERSON>hart from './SpectralChart'
import { useChart } from '../contexts/ChartContext'
import { Loader2, Settings, CheckCircle } from 'lucide-react'

import { Button } from './ui/button'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from './ui/dialog'
import { Label } from './ui/label'
import { RadioGroup, RadioGroupItem } from './ui/radio-group'

// Define ChartMode type locally to avoid dependency on types.ts
type ChartMode = 'T' | 'R' | 'Abs'

const ChartSection: React.FC = () => {
  const { state, dispatch } = useChart()
  const { activeMode, chartTitle, chartData, activeSeriesId } = state
  const [isLoading, setIsLoading] = useState(false)
  const [isSettingsOpen, setIsSettingsOpen] = useState(false)

  // Find the active series for display purposes
  const activeSeries = chartData.find((series) => series.id === activeSeriesId) || chartData[0]

  // 获取可见的曲线
  const visibleSeries = chartData.filter((series) => series.visible)
  
  // 检查是否有多条曲线
  const hasMultipleCurves = visibleSeries.length > 1

  const setActiveMode = useCallback(
    (mode: ChartMode): void => {
      if (mode === activeMode) return

      // 设置加载状态
      setIsLoading(true)

      // 使用requestAnimationFrame确保UI更新
      requestAnimationFrame(() => {
        // 切换模式
        dispatch({ type: 'SET_ACTIVE_MODE', payload: mode })

        // 切换模式后重新计算功能一表格中的所有行数据
        dispatch({ type: 'CALCULATE_ALL_FEATURE1_VALUES' })

        // 短暂延迟后移除加载状态，确保图表已经渲染完成
        setTimeout(() => {
          setIsLoading(false)
        }, 100)
      })
    },
    [activeMode, dispatch]
  )

  const handleActiveSeries = useCallback(
    (seriesId: string) => {
      dispatch({ type: 'SET_ACTIVE_SERIES', payload: seriesId })
    },
    [dispatch]
  )

  const handleConfirmSettings = useCallback(() => {
    // 重新计算功能一和功能二的所有数据
    dispatch({ type: 'CALCULATE_ALL_FEATURE1_VALUES' })
    dispatch({ type: 'CALCULATE_ALL_FEATURE2_VALUES' })
    // 关闭对话框
    setIsSettingsOpen(false)
  }, [dispatch])

  return (
    <Card className="flex-1 flex flex-col ">
      <CardHeader className="py-2 px-3 sm:px-4 border-b">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-0">
          <div className="flex items-center gap-2">
            <div className="!text-base  font-semibold truncate">{chartTitle}</div>

            {/* 只有当有多条曲线时才显示设置按钮 */}
            {hasMultipleCurves && (
              <Button 
                onClick={() => setIsSettingsOpen(true)}
                variant="outline" 
                size="sm"
                className="flex items-center gap-2"
              >
                <Settings className="h-4 w-4" />
                <span className="hidden sm:inline">计算曲线设置</span>
                <span className="sm:hidden">曲线设置</span>
                {activeSeries && (
                  <span className="text-xs py-0.5 px-1.5 bg-primary/10 text-primary rounded-full flex items-center gap-1">
                    <span className="w-2 h-2 rounded-full" style={{ backgroundColor: activeSeries.color }}></span>
                    {activeSeries.name.length > 8
                      ? activeSeries.name.substring(0, 8) + '...'
                      : activeSeries.name}
                  </span>
                )}
              </Button>
            )}
          </div>
          
          <Tabs value={activeMode} className="w-full sm:w-auto">
            <TabsList className="grid w-full grid-cols-3 sm:flex sm:w-auto">
              <TabsTrigger
                value="T"
                onClick={() => setActiveMode('T')}
                className="text-xs sm:text-sm px-2 sm:px-3"
                disabled={isLoading}
              >
                <span className="hidden sm:inline">透过率 (T)</span>
                <span className="sm:hidden">T</span>
              </TabsTrigger>
              <TabsTrigger
                value="R"
                onClick={() => setActiveMode('R')}
                className="text-xs sm:text-sm px-2 sm:px-3"
                disabled={isLoading}
              >
                <span className="hidden sm:inline">反射率 (R)</span>
                <span className="sm:hidden">R</span>
              </TabsTrigger>
              <TabsTrigger
                value="Abs"
                onClick={() => setActiveMode('Abs')}
                className="text-xs sm:text-sm px-2 sm:px-3"
                disabled={isLoading}
              >
                <span className="hidden sm:inline">吸收率 (Abs)</span>
                <span className="sm:hidden">Abs</span>
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </CardHeader>
      <CardContent className="flex-1 p-0 relative">
        {isLoading && (
          <div className="absolute inset-0 bg-background/80 flex items-center justify-center z-10">
            <Loader2 className="h-8 w-8 text-primary animate-spin" />
          </div>
        )}
        <SpectralChart />
      </CardContent>

      {/* 计算曲线设置对话框 */}
      <Dialog open={isSettingsOpen} onOpenChange={setIsSettingsOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="text-center">选择用于计算的曲线</DialogTitle>
          </DialogHeader>
          
          <div className="py-2 px-6 pb-6">
            <div className="space-y-4">
              <p className="text-sm text-muted-foreground text-center">
                选择一条曲线用于特征参数计算
              </p>
              
              {visibleSeries.length === 0 ? (
                <p className="text-sm text-muted-foreground">没有可用的曲线</p>
              ) : (
                <RadioGroup 
                  value={activeSeriesId || ''} 
                  onValueChange={handleActiveSeries}
                  className="space-y-2 max-h-[300px] overflow-y-auto pr-1"
                >
                  {visibleSeries.map((series) => (
                    <div 
                      key={series.id} 
                      className={`flex items-center space-x-2 border p-3 rounded-lg transition-all ${
                        series.id === activeSeriesId ? 'border-primary bg-primary/5' : ''
                      }`}
                    >
                      <RadioGroupItem value={series.id} id={`series-${series.id}`} />
                      <div className="flex items-center gap-2 flex-1">
                        <div
                          className="w-3 h-3 rounded-full"
                          style={{ backgroundColor: series.color }}
                        />
                        <Label htmlFor={`series-${series.id}`} className="text-sm cursor-pointer flex-1">
                          {series.name}
                        </Label>
                        {series.id === activeSeriesId && (
                          <CheckCircle className="h-4 w-4 text-primary" />
                        )}
                      </div>
                    </div>
                  ))}
                </RadioGroup>
              )}
            </div>
          </div>
          
          <DialogFooter className="sm:justify-between">
            <Button 
              variant="outline"
              onClick={() => setIsSettingsOpen(false)}
            >
              取消
            </Button>
            <Button
              onClick={handleConfirmSettings}
              className="min-w-[80px]"
            >
              确定
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  )
}

export default ChartSection
